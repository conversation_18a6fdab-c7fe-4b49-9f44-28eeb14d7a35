import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:get/get.dart';

import 'package:lc_work_flow/screen/dashboard/screens/add_customer/borrower_info_step.dart';
import 'package:lc_work_flow/screen/dashboard/screens/add_customer/loan_info_step.dart';
import 'package:lc_work_flow/screen/dashboard/screens/add_customer/documents_step.dart';
// import 'package:lc_work_flow/screen/dashboard/screens/add_customer/review_step.dart_backup';
import 'package:lc_work_flow/models/collateral_type.dart';
import 'package:lc_work_flow/models/product_type.dart';
import 'package:lc_work_flow/models/document_type.dart';
import 'package:lc_work_flow/screen/dashboard/screens/add_customer/review_step.dart';
import 'package:lc_work_flow/screen/dashboard/screens/add_customer/step_progress_bar.dart';
import 'package:lc_work_flow/services/local_storage_service.dart';
import 'package:lc_work_flow/models/customer_model.dart';
import 'package:lc_work_flow/services/khmer_text_recognizer.dart';
import 'package:lc_work_flow/services/mrz_parser.dart';

class AddCustomerScreen extends StatefulWidget {
  const AddCustomerScreen({super.key});

  @override
  State<AddCustomerScreen> createState() => _AddCustomerScreenState();
}

class _AddCustomerScreenState extends State<AddCustomerScreen> {
  int _currentStep = 0;
  final List<GlobalKey<FormState>> _formKeys = List.generate(
    4,
    (_) => GlobalKey<FormState>(),
  );

  final LocalStorageService _localStorageService = LocalStorageService();
  String? _currentDraftId;

  // Controllers for personal info
  final TextEditingController _fullNameKhmerController =
      TextEditingController();
  final TextEditingController _fullNameLatinController =
      TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _idNumberController = TextEditingController();

  // Borrower info step required state
  DocumentType? _selectedDocumentType;

  // Loan info controllers
  final TextEditingController _requestedAmountController =
      TextEditingController();
  final TextEditingController _loanTermController = TextEditingController();
  DateTime? _dateOfBirth;
  DateTime? _disbursementDate;
  ProductType? _selectedProductType;
  final Set<CollateralType> _selectedCollateralTypes = <CollateralType>{};
  final Map<CollateralType, List<File>> _collateralImages =
      <CollateralType, List<File>>{};
  // Guarantor section controllers
  final TextEditingController _guarantorNameController =
      TextEditingController();
  final TextEditingController _guarantorPhoneController =
      TextEditingController();
  final FocusNode _guarantorNameFocusNode = FocusNode();
  final FocusNode _guarantorPhoneFocusNode = FocusNode();
  bool get _showGuarantorSection =>
      _selectedCollateralTypes.contains(CollateralType.guarantor) ||
      _selectedCollateralTypes.contains(CollateralType.coBorrower);

  List<File> _idCardImages = [];

  @override
  // TODO: [MEDIUM] Add proper cleanup for all controllers
  // - Cancel any pending operations
  // - Dispose all animation controllers
  // - Clear any caches
  void dispose() {
    _fullNameKhmerController.dispose();
    _fullNameLatinController.dispose();
    _phoneController.dispose();
    _idNumberController.dispose();
    _requestedAmountController.dispose();
    _loanTermController.dispose();
    _guarantorNameController.dispose();
    _guarantorPhoneController.dispose();
    _guarantorNameFocusNode.dispose();
    _guarantorPhoneFocusNode.dispose();
    super.dispose();
  }

  void _goToNextStep() {
    if (_formKeys[_currentStep].currentState?.validate() ?? false) {
      if (_currentStep < 3) {
        setState(() => _currentStep++);
      } else {
        _submitForm();
      }
    }
  }

  void _goToPreviousStep() {
    if (_currentStep > 0) {
      setState(() => _currentStep--);
    }
  }

  // Submit form and save to local storage
  Future<void> _submitForm() async {
    try {
      final localStorageService = LocalStorageService();

      // Prepare image paths
      final idCardImagePaths = _idCardImages.map((file) => file.path).toList();

      // Save the application to local storage
      final applicationId = await localStorageService.saveDraftApplication(
        idCardType: _selectedDocumentType?.toString(),
        idNumber: _idNumberController.text,
        fullNameKhmer: _fullNameKhmerController.text,
        fullNameLatin: _fullNameLatinController.text,
        phone: _phoneController.text,
        dateOfBirth: _dateOfBirth,
        portfolioOfficerName: 'Current Officer', // TODO: Get from user session
        requestedAmount: double.tryParse(_requestedAmountController.text),
        loanPurposes: [], // TODO: Implement loan purposes selection
        purposeDetails:
            'Application purpose', // TODO: Add purpose details field
        productType: _selectedProductType?.toString(),
        desiredLoanTerm: _loanTermController.text,
        requestedDisbursementDate: _disbursementDate,
        guarantorName: _guarantorNameController.text,
        guarantorPhone: _guarantorPhoneController.text,
        idCardImages: idCardImagePaths,
        borrowerNidPhoto:
            _collateralImages[CollateralType.nid]?.isNotEmpty == true
                ? _collateralImages[CollateralType.nid]![0].path
                : null,
        borrowerHomePhoto:
            _collateralImages[CollateralType.homePhoto]?.isNotEmpty == true
                ? _collateralImages[CollateralType.homePhoto]![0].path
                : null,
        borrowerBusinessPhoto:
            _collateralImages[CollateralType.businessPhoto]?.isNotEmpty == true
                ? _collateralImages[CollateralType.businessPhoto]![0].path
                : null,
        guarantorNidPhoto:
            _collateralImages[CollateralType.guarantor]?.isNotEmpty == true
                ? _collateralImages[CollateralType.guarantor]![0].path
                : null,
        guarantorHomePhoto: null, // TODO: Add guarantor home photo handling
        guarantorBusinessPhoto:
            _collateralImages[CollateralType.guarantorBusinessPhoto]
                        ?.isNotEmpty ==
                    true
                ? _collateralImages[CollateralType.guarantorBusinessPhoto]![0]
                    .path
                : null,
        profilePhoto: null, // TODO: Add profile photo handling
        selectedCollateralTypes:
            _selectedCollateralTypes.map((type) => type.toString()).toList(),
      );

      // Submit the application (mark as submitted)
      await localStorageService.submitApplication(applicationId);

      // Create a Customer object for the controller
      final customer = Customer(
        id: applicationId,
        name:
            _fullNameLatinController.text.isNotEmpty
                ? _fullNameLatinController.text
                : _fullNameKhmerController.text,
        phone: _phoneController.text,
        nid: _idNumberController.text,
        loanAmount: double.tryParse(_requestedAmountController.text) ?? 0.0,
        loanStartDate: DateTime.now(),
        loanStatus: LoanStatus.pending,
        fullNameKhmer: _fullNameKhmerController.text,
        fullNameLatin: _fullNameLatinController.text,
        dateOfBirth: _dateOfBirth,
        idNumber: _idNumberController.text,
        requestedAmount: double.tryParse(_requestedAmountController.text),
        productType: _selectedProductType,
        desiredLoanTerm: _loanTermController.text,
        requestedDisbursementDate: _disbursementDate,
        guarantorName: _guarantorNameController.text,
        guarantorPhone: _guarantorPhoneController.text,
      );

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Application submitted successfully! ID: $applicationId',
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );

      // Return the customer object to the previous screen
      Navigator.of(context).pop(customer);
    } catch (e) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error submitting application: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // TODO: [MEDIUM] Enhance document scanning
  // - Add support for multiple document types
  // - Improve OCR accuracy
  // - Add loading indicator during processing
  // - Handle edge cases (blurry images, low light, etc.)
  Future<void> _scanDocument() async {
    try {
      // Pick image from camera
      final picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 90,
      );

      if (image != null) {
        final imageFile = File(image.path);

        // Store the captured image
        setState(() {
          _idCardImages = List<File>.from(_idCardImages)..add(imageFile);
        });

        // OCR: Recognize text lines
        final lines = await KhmerTextRecognizer.recognizeTextLines(imageFile);

        if (_selectedDocumentType == DocumentType.khmerId ||
            _selectedDocumentType == DocumentType.passport) {
          // Try MRZ extraction first
          final mrzLines = KhmerTextRecognizer.extractMRZLines(lines);
          final mrzResult = MRZParser.parse(mrzLines);
          if (mrzResult != null) {
            setState(() {
              _fullNameLatinController.text = mrzResult.fullNameLatin;
              _idNumberController.text = mrzResult.idNumber;
              if (mrzResult.dateOfBirth.isNotEmpty) {
                _dateOfBirth = DateTime.tryParse(mrzResult.dateOfBirth);
              }
            });
            Get.snackbar(
              'Success',
              'MRZ recognized and fields autofilled',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.green,
              colorText: Colors.white,
            );
            return;
          } else {
            // Fallback: try OCR line parsing
            final fullName = KhmerTextRecognizer.extractFullNameLatin(lines);
            final idNumber = KhmerTextRecognizer.extractIdNumber(lines);
            final dob = KhmerTextRecognizer.extractDateOfBirth(lines);
            setState(() {
              if (fullName != null) _fullNameLatinController.text = fullName;
              if (idNumber != null) _idNumberController.text = idNumber;
              if (dob != null) {
                _dateOfBirth = DateTime.tryParse(_formatDate(dob));
              }
            });
            Get.snackbar(
              'Partial Success',
              'Some fields autofilled from OCR text.',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.orange,
              colorText: Colors.white,
            );
            return;
          }
        } else if (_selectedDocumentType == DocumentType.driversLicense) {
          // Use OCR line parsing helpers
          final fullName = KhmerTextRecognizer.extractFullNameLatin(lines);
          final idNumber = KhmerTextRecognizer.extractIdNumber(lines);
          final dob = KhmerTextRecognizer.extractDateOfBirth(lines);
          setState(() {
            if (fullName != null) _fullNameLatinController.text = fullName;
            if (idNumber != null) _idNumberController.text = idNumber;
            if (dob != null) _dateOfBirth = DateTime.tryParse(_formatDate(dob));
          });
          if (fullName != null || idNumber != null || dob != null) {
            Get.snackbar(
              'Success',
              'Fields autofilled from OCR text.',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.green,
              colorText: Colors.white,
            );
          } else {
            Get.snackbar(
              'Not Found',
              'Could not extract fields. Please try again or enter manually.',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.orange,
              colorText: Colors.white,
            );
          }
          return;
        }
        // If no document type matched or nothing found
        Get.snackbar(
          'Not Found',
          'Could not extract fields. Please try again or enter manually.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
      } else {
        // User cancelled the image picker
        Get.snackbar(
          'Cancelled',
          'Document scanning was cancelled',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      // Show error message
      Get.snackbar(
        'Error',
        'Failed to scan document: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Helper to convert DD-MM-YYYY to YYYY-MM-DD for DateTime.parse
  String _formatDate(String date) {
    final match = RegExp(r'^(\d{2})-(\d{2})-(\d{4})').firstMatch(date);
    if (match != null) {
      return '${match.group(3)}-${match.group(2)}-${match.group(1)}';
    }
    return date;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: Colors.grey[50],
      appBar: _buildModernAppBar(context),
      body: SafeArea(
        child: Column(
          children: [
            StepProgressBar(
              currentStep: _currentStep,
              steps: const [
                'អតិថិជន',
                'កខ្ចីប្រាក់',
                'ឯកសារយោង',
                'ផ្ទៀងផ្ទាត់',
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20.0,
                  vertical: 8.0,
                ),
                child: _buildStepContent(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Modern, space-efficient app bar design
  PreferredSizeWidget _buildModernAppBar(BuildContext context) {
    return PreferredSize(
      preferredSize: const Size.fromHeight(64),
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF12306E), Color(0xFF1E4A8C)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          centerTitle: false,
          titleSpacing: 16,
          leading: Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: const Icon(
                Icons.arrow_back_ios_new,
                color: Colors.white,
                size: 20,
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFF6B900),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.person_add_alt_1,
                  color: Color(0xFF12306E),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'New Application',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'ពាក្យស្នើ​សុំខ្ចីប្រាក់ និងដាក់បញ្ចាំ',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }



  // TODO: [LOW] Refactor step content into separate widget files
  // - Move each step to its own file for better maintainability
  // - Extract common widgets (section cards, review tiles, etc.)
  // - Add error boundaries for each step
  Widget _buildStepContent() {
    final isLastStep = _currentStep == 3;
    Widget content;
    switch (_currentStep) {
      case 0:
        content = Form(
          key: _formKeys[0],
          child: BorrowerInfoStep(
            fullNameKhmerController: _fullNameKhmerController,
            fullNameLatinController: _fullNameLatinController,
            phoneController: _phoneController,
            idNumberController: _idNumberController,
            selectedDocumentType: _selectedDocumentType,
            onDocumentTypeChanged:
                (docType) => setState(() => _selectedDocumentType = docType),
            dateOfBirth: _dateOfBirth,
            onDateOfBirthChanged: (date) => setState(() => _dateOfBirth = date),
            onScanDocument: _scanDocument,
          ),
        );
        break;
      case 1:
        content = Form(
          key: _formKeys[1],
          child: LoanInfoStep(
            requestedAmountController: _requestedAmountController,
            selectedProductType: _selectedProductType,
            onProductTypeChanged:
                (type) => setState(() => _selectedProductType = type),
            loanTermController: _loanTermController,
            disbursementDate: _disbursementDate,
            onDisbursementDateChanged:
                (date) => setState(() => _disbursementDate = date),
            selectedCollateralTypes: _selectedCollateralTypes,
            onCollateralTypeChanged:
                (type, selected) => setState(() {
                  if (selected) {
                    _selectedCollateralTypes.add(type);
                  } else {
                    _selectedCollateralTypes.remove(type);
                  }
                }),
            guarantorNameController: _guarantorNameController,
            guarantorPhoneController: _guarantorPhoneController,
            guarantorNameFocusNode: _guarantorNameFocusNode,
            guarantorPhoneFocusNode: _guarantorPhoneFocusNode,
            showGuarantorSection: _showGuarantorSection,
          ),
        );
        break;
      case 2:
        content = Form(
          key: _formKeys[2],
          child: DocumentsStep(
            idCardImages: _idCardImages,
            onIdCardImagesChanged:
                (images) => setState(() => _idCardImages = images),
            collateralImages: _collateralImages,
            selectedCollateralTypes: _selectedCollateralTypes,
            onImagePicked:
                (type, photos) =>
                    setState(() => _collateralImages[type] = photos),
          ),
        );
        break;
      case 3:
      default:
        content = Form(
          key: _formKeys[3],
          child: ReviewStep(
            borrowerInfo: {
              'documentType': _selectedDocumentType?.displayName ?? '',
              'idNumber': _idNumberController.text,
              'fullNameKhmer': _fullNameKhmerController.text,
              'fullNameLatin': _fullNameLatinController.text,
              'phone': _phoneController.text,
              'dateOfBirth':
                  _dateOfBirth != null
                      ? _dateOfBirth.toString().split(' ')[0]
                      : '',
            },
            loanInfo: {
              'requestedAmount': _requestedAmountController.text,
              'productType': _selectedProductType?.displayName ?? '',
              'loanTerm': _loanTermController.text,
              'loanPurposes':
                  [], // TODO: Fill with your loan purposes if available
              'purposeDetails': '', // TODO: Fill if you have this field
              'disbursementDate':
                  _disbursementDate != null
                      ? _disbursementDate.toString().split(' ')[0]
                      : '',
            },
            collateralList:
                _selectedCollateralTypes
                    .map(
                      (type) => {
                        'type': type.displayName,
                        'description':
                            '', // TODO: Fill if you have collateral descriptions
                        'image':
                            (_collateralImages[type]?.isNotEmpty ?? false)
                                ? _collateralImages[type]![0].path
                                : null,
                      },
                    )
                    .toList(),
            guarantor:
                _showGuarantorSection
                    ? {
                      'name': _guarantorNameController.text,
                      'phone': _guarantorPhoneController.text,
                    }
                    : null,
            documents:
                _idCardImages
                    .map((file) => {'type': 'ID Card', 'image': file.path})
                    .toList(),
            photos: {
              'borrowerNidPhoto':
                  (_collateralImages[CollateralType.nid]?.isNotEmpty ?? false)
                      ? _collateralImages[CollateralType.nid]![0].path
                      : null,
              'borrowerHomePhoto':
                  (_collateralImages[CollateralType.homePhoto]?.isNotEmpty ??
                          false)
                      ? _collateralImages[CollateralType.homePhoto]![0].path
                      : null,
              'borrowerBusinessPhoto':
                  (_collateralImages[CollateralType.businessPhoto]
                              ?.isNotEmpty ??
                          false)
                      ? _collateralImages[CollateralType.businessPhoto]![0].path
                      : null,
              'guarantorNidPhoto':
                  (_collateralImages[CollateralType.guarantor]?.isNotEmpty ??
                          false)
                      ? _collateralImages[CollateralType.guarantor]![0].path
                      : null,
              'guarantorHomePhoto':
                  (_collateralImages[CollateralType.guarantor]?.isNotEmpty ??
                          false)
                      ? _collateralImages[CollateralType.guarantor]![0].path
                      : null,
              'guarantorBusinessPhoto':
                  (_collateralImages[CollateralType.guarantorBusinessPhoto]
                              ?.isNotEmpty ??
                          false)
                      ? _collateralImages[CollateralType
                              .guarantorBusinessPhoto]![0]
                          .path
                      : null,
              'profilePhoto': null, // TODO: Fill if you have a profile photo
            },
            officerName: '', // TODO: Fill if you have this field
            onBack: _goToPreviousStep,
            onConfirm: _submitForm,
          ),
        );
        break;
    }

    return Column(
      children: [
        // Enhanced content container
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.06),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: content,
        ),
        const SizedBox(height: 32),
        // Enhanced navigation buttons
        _buildNavigationButtons(isLastStep),
        const SizedBox(height: 20),
      ],
    );
  }

  /// Enhanced navigation buttons with modern design
  Widget _buildNavigationButtons(bool isLastStep) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          if (_currentStep > 0) ...[
            Expanded(
              flex: 2,
              child: Container(
                height: 52,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.grey[300]!, width: 1.5),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(16),
                    onTap: _goToPreviousStep,
                    child: const Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.arrow_back_ios,
                            size: 18,
                            color: Colors.grey,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Back',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
          ],
          Expanded(
            flex: 3,
            child: Container(
              height: 52,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF12306E), Color(0xFF1E4A8C)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF12306E).withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(16),
                  onTap: _goToNextStep,
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Flexible(
                          child: Text(
                            isLastStep ? 'Submit Application' : 'Continue',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          isLastStep
                              ? Icons.check_circle
                              : Icons.arrow_forward_ios,
                          size: 18,
                          color: Colors.white,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
