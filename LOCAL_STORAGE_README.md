# Local Storage Implementation for Customer Applications

This document describes the local storage implementation for temporarily storing customer loan applications before backend API integration.

## Overview

The local storage system allows you to:
- Save customer applications locally on the device
- Submit applications (mark as submitted for demo purposes)
- View saved and submitted applications
- Showcase the complete workflow to clients without a backend

## Components

### 1. Database Helper (`lib/services/database_helper.dart`)
- Uses SQLite for local data storage
- Creates a `customer_applications` table with all necessary fields
- Provides CRUD operations for applications

### 2. Local Storage Service (`lib/services/local_storage_service.dart`)
- High-level service for managing customer applications
- Handles data conversion between forms and database
- Provides methods for saving drafts and submitting applications

### 3. Updated Customer Model (`lib/models/customer_model.dart`)
- Added JSON serialization methods (`toJson()` and `from<PERSON>son()`)
- Added static parsing methods for enum types
- Maintains backward compatibility with existing code

### 4. Enhanced Add Customer Screen (`lib/screen/dashboard/screens/add_customer_screen.dart`)
- Integrated with local storage service
- Saves applications when submitted
- Returns Customer object to dashboard for immediate display

### 5. Saved Applications Screen (`lib/screen/dashboard/screens/saved_applications_screen.dart`)
- Displays all saved applications
- Tabs for "Submitted" and "Draft" applications
- Detailed view for each application
- Pull-to-refresh functionality

## How to Use

### 1. Submitting Applications
1. Fill out the customer application form
2. Click "Submit Application" on the final step
3. Application is saved locally and marked as "submitted"
4. Success message shows the application ID
5. Customer is added to the dashboard list

### 2. Viewing Saved Applications
1. Go to Dashboard
2. Click the profile menu (top-right)
3. Select "Saved Applications"
4. Switch between "Submitted" and "Draft" tabs
5. Tap any application to view details

### 3. Demo Functionality
Use the demo class (`lib/demo/local_storage_demo.dart`) to:
```dart
// Create sample data
await LocalStorageDemo.runCompleteDemo();

// Check application counts
await LocalStorageDemo.printApplicationCounts();
```

## Database Schema

The `customer_applications` table includes:

**Basic Info:**
- `id` (TEXT PRIMARY KEY)
- `created_at`, `updated_at` (TEXT)
- `status` (TEXT: 'draft' or 'submitted')

**Borrower Information:**
- `id_card_type`, `id_number`
- `full_name_khmer`, `full_name_latin`
- `phone`, `date_of_birth`
- `portfolio_officer_name`

**Loan Details:**
- `requested_amount`, `loan_purposes` (JSON)
- `purpose_details`, `product_type`
- `desired_loan_term`, `requested_disbursement_date`

**Guarantor Information:**
- `guarantor_name`, `guarantor_phone`

**File Paths:**
- `id_card_images` (JSON array)
- `borrower_nid_photo`, `borrower_home_photo`, `borrower_business_photo`
- `guarantor_nid_photo`, `guarantor_home_photo`, `guarantor_business_photo`
- `profile_photo`

**Collateral:**
- `selected_collateral_types` (JSON array)

## Benefits for Client Demo

1. **Complete Workflow**: Shows the entire application process
2. **Data Persistence**: Applications are saved between app sessions
3. **Realistic Experience**: Mimics real backend behavior
4. **Offline Capability**: Works without internet connection
5. **Easy Migration**: When backend is ready, data can be easily exported/migrated

## Future Backend Integration

When implementing the actual backend:

1. **Export Data**: Use the JSON serialization to export existing applications
2. **API Integration**: Replace local storage calls with API calls
3. **Sync Mechanism**: Implement sync between local and remote data
4. **Migration Path**: Convert local applications to server format

## File Locations

- Database: Stored in app's documents directory
- Images: File paths stored in database, actual files in app storage
- Preferences: Auto-save drafts in SharedPreferences

## Dependencies Added

```yaml
dependencies:
  shared_preferences: ^2.5.3  # For simple key-value storage
  sqflite: ^2.4.2            # For SQLite database
  path: ^1.9.1               # For file path operations
  path_provider: ^2.1.5      # For getting app directories
```

## Testing

To test the implementation:

1. Run the app
2. Create a new customer application
3. Fill out all steps and submit
4. Check "Saved Applications" to see the stored data
5. Restart the app to verify persistence

The local storage is now fully functional and ready for client demonstration!
