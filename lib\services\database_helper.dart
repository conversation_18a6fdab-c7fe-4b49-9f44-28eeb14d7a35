import 'dart:async';
import 'dart:io';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart' if (dart.library.io) 'package:path_provider/path_provider.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    // Get the documents directory path
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, 'customer_applications.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create customer applications table
    await db.execute('''
      CREATE TABLE customer_applications (
        id TEXT PRIMARY KEY,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'draft',
        
        -- Borrower Information
        id_card_type TEXT,
        id_number TEXT,
        full_name_khmer TEXT,
        full_name_latin TEXT,
        phone TEXT,
        date_of_birth TEXT,
        portfolio_officer_name TEXT,
        
        -- Loan Details
        requested_amount REAL,
        loan_purposes TEXT, -- JSON array
        purpose_details TEXT,
        product_type TEXT,
        desired_loan_term TEXT,
        requested_disbursement_date TEXT,
        
        -- Guarantor Information
        guarantor_name TEXT,
        guarantor_phone TEXT,
        
        -- File paths (stored as JSON)
        id_card_images TEXT, -- JSON array of file paths
        borrower_nid_photo TEXT,
        borrower_home_photo TEXT,
        borrower_business_photo TEXT,
        guarantor_nid_photo TEXT,
        guarantor_home_photo TEXT,
        guarantor_business_photo TEXT,
        profile_photo TEXT,
        
        -- Collateral types (stored as JSON)
        selected_collateral_types TEXT -- JSON array
      )
    ''');

    // Create index for faster queries
    await db.execute('''
      CREATE INDEX idx_customer_applications_status ON customer_applications(status)
    ''');
    
    await db.execute('''
      CREATE INDEX idx_customer_applications_created_at ON customer_applications(created_at)
    ''');
  }

  // Insert a new customer application
  Future<int> insertCustomerApplication(Map<String, dynamic> application) async {
    final db = await database;
    final now = DateTime.now().toIso8601String();
    
    application['created_at'] = now;
    application['updated_at'] = now;
    application['status'] = application['status'] ?? 'draft';
    
    return await db.insert('customer_applications', application);
  }

  // Update an existing customer application
  Future<int> updateCustomerApplication(String id, Map<String, dynamic> application) async {
    final db = await database;
    application['updated_at'] = DateTime.now().toIso8601String();
    
    return await db.update(
      'customer_applications',
      application,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Get all customer applications
  Future<List<Map<String, dynamic>>> getAllCustomerApplications() async {
    final db = await database;
    return await db.query(
      'customer_applications',
      orderBy: 'created_at DESC',
    );
  }

  // Get customer applications by status
  Future<List<Map<String, dynamic>>> getCustomerApplicationsByStatus(String status) async {
    final db = await database;
    return await db.query(
      'customer_applications',
      where: 'status = ?',
      whereArgs: [status],
      orderBy: 'created_at DESC',
    );
  }

  // Get a specific customer application
  Future<Map<String, dynamic>?> getCustomerApplication(String id) async {
    final db = await database;
    final results = await db.query(
      'customer_applications',
      where: 'id = ?',
      whereArgs: [id],
    );
    
    return results.isNotEmpty ? results.first : null;
  }

  // Delete a customer application
  Future<int> deleteCustomerApplication(String id) async {
    final db = await database;
    return await db.delete(
      'customer_applications',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Get draft applications count
  Future<int> getDraftApplicationsCount() async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM customer_applications WHERE status = ?',
      ['draft'],
    );
    return result.first['count'] as int;
  }

  // Get submitted applications count
  Future<int> getSubmittedApplicationsCount() async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM customer_applications WHERE status = ?',
      ['submitted'],
    );
    return result.first['count'] as int;
  }

  // Close database
  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
