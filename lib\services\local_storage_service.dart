import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:lc_work_flow/services/database_helper.dart';
import 'package:lc_work_flow/models/customer_model.dart';
import 'package:lc_work_flow/models/collateral_type.dart';
import 'package:lc_work_flow/models/product_type.dart';
import 'package:lc_work_flow/models/document_type.dart';

class LocalStorageService {
  static final LocalStorageService _instance = LocalStorageService._internal();
  static SharedPreferences? _prefs;
  static DatabaseHelper? _dbHelper;

  LocalStorageService._internal();

  factory LocalStorageService() => _instance;

  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
    _dbHelper ??= DatabaseHelper();
  }

  // Save customer application as draft
  Future<String> saveDraftApplication({
    required String? idCardType,
    required String? idNumber,
    required String? fullNameKhmer,
    required String? fullNameLatin,
    required String? phone,
    required DateTime? dateOfBirth,
    required String? portfolioOfficerName,
    required double? requestedAmount,
    required List<String>? loanPurposes,
    required String? purposeDetails,
    required String? productType,
    required String? desiredLoanTerm,
    required DateTime? requestedDisbursementDate,
    required String? guarantorName,
    required String? guarantorPhone,
    required List<String>? idCardImages,
    required String? borrowerNidPhoto,
    required String? borrowerHomePhoto,
    required String? borrowerBusinessPhoto,
    required String? guarantorNidPhoto,
    required String? guarantorHomePhoto,
    required String? guarantorBusinessPhoto,
    required String? profilePhoto,
    required List<String>? selectedCollateralTypes,
    String? existingId,
  }) async {
    await init();
    
    final applicationId = existingId ?? DateTime.now().millisecondsSinceEpoch.toString();
    
    final applicationData = {
      'id': applicationId,
      'status': 'draft',
      'id_card_type': idCardType,
      'id_number': idNumber,
      'full_name_khmer': fullNameKhmer,
      'full_name_latin': fullNameLatin,
      'phone': phone,
      'date_of_birth': dateOfBirth?.toIso8601String(),
      'portfolio_officer_name': portfolioOfficerName,
      'requested_amount': requestedAmount,
      'loan_purposes': jsonEncode(loanPurposes ?? []),
      'purpose_details': purposeDetails,
      'product_type': productType,
      'desired_loan_term': desiredLoanTerm,
      'requested_disbursement_date': requestedDisbursementDate?.toIso8601String(),
      'guarantor_name': guarantorName,
      'guarantor_phone': guarantorPhone,
      'id_card_images': jsonEncode(idCardImages ?? []),
      'borrower_nid_photo': borrowerNidPhoto,
      'borrower_home_photo': borrowerHomePhoto,
      'borrower_business_photo': borrowerBusinessPhoto,
      'guarantor_nid_photo': guarantorNidPhoto,
      'guarantor_home_photo': guarantorHomePhoto,
      'guarantor_business_photo': guarantorBusinessPhoto,
      'profile_photo': profilePhoto,
      'selected_collateral_types': jsonEncode(selectedCollateralTypes ?? []),
    };

    if (existingId != null) {
      await _dbHelper!.updateCustomerApplication(applicationId, applicationData);
    } else {
      await _dbHelper!.insertCustomerApplication(applicationData);
    }

    return applicationId;
  }

  // Submit customer application
  Future<bool> submitApplication(String applicationId) async {
    await init();
    
    final result = await _dbHelper!.updateCustomerApplication(
      applicationId,
      {'status': 'submitted'},
    );
    
    return result > 0;
  }

  // Get all draft applications
  Future<List<Map<String, dynamic>>> getDraftApplications() async {
    await init();
    return await _dbHelper!.getCustomerApplicationsByStatus('draft');
  }

  // Get all submitted applications
  Future<List<Map<String, dynamic>>> getSubmittedApplications() async {
    await init();
    return await _dbHelper!.getCustomerApplicationsByStatus('submitted');
  }

  // Get specific application
  Future<Map<String, dynamic>?> getApplication(String id) async {
    await init();
    return await _dbHelper!.getCustomerApplication(id);
  }

  // Delete application
  Future<bool> deleteApplication(String id) async {
    await init();
    final result = await _dbHelper!.deleteCustomerApplication(id);
    return result > 0;
  }

  // Get applications count
  Future<Map<String, int>> getApplicationsCounts() async {
    await init();
    final draftCount = await _dbHelper!.getDraftApplicationsCount();
    final submittedCount = await _dbHelper!.getSubmittedApplicationsCount();
    
    return {
      'draft': draftCount,
      'submitted': submittedCount,
      'total': draftCount + submittedCount,
    };
  }

  // Convert database record to Customer model (for display purposes)
  Customer convertToCustomer(Map<String, dynamic> data) {
    return Customer(
      id: data['id'] ?? '',
      name: data['full_name_latin'] ?? data['full_name_khmer'] ?? 'Unknown',
      phone: data['phone'] ?? '',
      nid: data['id_number'] ?? '',
      loanAmount: data['requested_amount']?.toDouble() ?? 0.0,
      loanStartDate: DateTime.tryParse(data['created_at'] ?? '') ?? DateTime.now(),
      loanStatus: data['status'] == 'submitted' ? LoanStatus.pending : LoanStatus.draft,
      
      // Extended fields
      idCardType: _parseIdCardType(data['id_card_type']),
      fullNameKhmer: data['full_name_khmer'],
      fullNameLatin: data['full_name_latin'],
      dateOfBirth: DateTime.tryParse(data['date_of_birth'] ?? ''),
      idNumber: data['id_number'],
      portfolioOfficerName: data['portfolio_officer_name'],
      requestedAmount: data['requested_amount']?.toDouble(),
      purposeDetails: data['purpose_details'],
      productType: _parseProductType(data['product_type']),
      desiredLoanTerm: data['desired_loan_term'],
      requestedDisbursementDate: DateTime.tryParse(data['requested_disbursement_date'] ?? ''),
      guarantorName: data['guarantor_name'],
      guarantorPhone: data['guarantor_phone'],
      borrowerNidPhotoPath: data['borrower_nid_photo'],
      borrowerHomeOrLandPhotoPath: data['borrower_home_photo'],
      borrowerBusinessPhotoPath: data['borrower_business_photo'],
      guarantorNidPhotoPath: data['guarantor_nid_photo'],
      guarantorHomeOrLandPhotoPath: data['guarantor_home_photo'],
      guarantorBusinessPhotoPath: data['guarantor_business_photo'],
      profilePhotoPath: data['profile_photo'],
    );
  }

  IdCardType? _parseIdCardType(String? type) {
    if (type == null) return null;
    return IdCardType.values.firstWhere(
      (e) => e.toString().split('.').last == type,
      orElse: () => IdCardType.cambodianIdentity,
    );
  }

  ProductType? _parseProductType(String? type) {
    if (type == null) return null;
    return ProductType.values.firstWhere(
      (e) => e.toString().split('.').last == type,
      orElse: () => ProductType.microLoan,
    );
  }

  // Save auto-draft (for auto-save functionality)
  Future<void> saveAutoDraft(Map<String, dynamic> formData) async {
    await init();
    const autoDraftKey = 'auto_draft_application';
    final jsonData = jsonEncode(formData);
    await _prefs!.setString(autoDraftKey, jsonData);
  }

  // Get auto-draft
  Future<Map<String, dynamic>?> getAutoDraft() async {
    await init();
    const autoDraftKey = 'auto_draft_application';
    final jsonData = _prefs!.getString(autoDraftKey);
    if (jsonData != null) {
      return jsonDecode(jsonData);
    }
    return null;
  }

  // Clear auto-draft
  Future<void> clearAutoDraft() async {
    await init();
    const autoDraftKey = 'auto_draft_application';
    await _prefs!.remove(autoDraftKey);
  }
}
