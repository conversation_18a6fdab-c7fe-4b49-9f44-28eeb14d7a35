import 'package:lc_work_flow/services/local_storage_service.dart';

/// Demo function to test local storage functionality
/// This can be called from anywhere in your app to test the storage
class LocalStorageDemo {
  static final LocalStorageService _storageService = LocalStorageService();

  /// Create a sample application for testing
  static Future<String> createSampleApplication() async {
    final applicationId = await _storageService.saveDraftApplication(
      idCardType: 'IdCardType.cambodianIdentity',
      idNumber: '123456789012',
      fullNameKhmer: 'សុខា ចាន',
      fullNameLatin: 'Sokha Chan',
      phone: '+855 12 345 678',
      dateOfBirth: DateTime(1990, 5, 15),
      portfolioOfficerName: 'Demo Officer',
      requestedAmount: 5000.0,
      loanPurposes: ['LoanPurposeType.commerce'],
      purposeDetails: 'Expand small business',
      productType: 'ProductType.monthly',
      desiredLoanTerm: '12 months',
      requestedDisbursementDate: DateTime.now().add(const Duration(days: 7)),
      guarantorName: '<PERSON><PERSON>',
      guarantorPhone: '+855 23 456 789',
      idCardImages: [],
      borrowerNidPhoto: null,
      borrowerHomePhoto: null,
      borrowerBusinessPhoto: null,
      guarantorNidPhoto: null,
      guarantorHomePhoto: null,
      guarantorBusinessPhoto: null,
      profilePhoto: null,
      selectedCollateralTypes: ['CollateralType.nid', 'CollateralType.guarantor'],
    );

    print('Created sample application with ID: $applicationId');
    return applicationId;
  }

  /// Submit the sample application
  static Future<void> submitSampleApplication(String applicationId) async {
    final success = await _storageService.submitApplication(applicationId);
    if (success) {
      print('Successfully submitted application: $applicationId');
    } else {
      print('Failed to submit application: $applicationId');
    }
  }

  /// Get all applications and print them
  static Future<void> printAllApplications() async {
    final drafts = await _storageService.getDraftApplications();
    final submitted = await _storageService.getSubmittedApplications();

    print('\n=== DRAFT APPLICATIONS ===');
    for (final draft in drafts) {
      print('ID: ${draft['id']}');
      print('Name: ${draft['full_name_latin'] ?? draft['full_name_khmer']}');
      print('Amount: \$${draft['requested_amount']}');
      print('Created: ${draft['created_at']}');
      print('---');
    }

    print('\n=== SUBMITTED APPLICATIONS ===');
    for (final app in submitted) {
      print('ID: ${app['id']}');
      print('Name: ${app['full_name_latin'] ?? app['full_name_khmer']}');
      print('Amount: \$${app['requested_amount']}');
      print('Created: ${app['created_at']}');
      print('---');
    }
  }

  /// Get application counts
  static Future<void> printApplicationCounts() async {
    final counts = await _storageService.getApplicationsCounts();
    print('\n=== APPLICATION COUNTS ===');
    print('Draft: ${counts['draft']}');
    print('Submitted: ${counts['submitted']}');
    print('Total: ${counts['total']}');
  }

  /// Run a complete demo
  static Future<void> runCompleteDemo() async {
    print('Starting Local Storage Demo...\n');

    // Create a sample application
    final applicationId = await createSampleApplication();

    // Print current state
    await printApplicationCounts();
    await printAllApplications();

    // Submit the application
    await submitSampleApplication(applicationId);

    // Print updated state
    print('\nAfter submission:');
    await printApplicationCounts();
    await printAllApplications();

    print('\nDemo completed!');
  }
}
