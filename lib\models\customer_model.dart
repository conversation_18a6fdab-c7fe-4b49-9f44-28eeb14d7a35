import 'package:flutter/material.dart';
import 'package:lc_work_flow/models/product_type.dart' as product_type;

enum LoanStatus { draft, pending, approved, disbursed, completed, rejected }

enum IdCardType { nid, passport, driverLicense, cambodianIdentity, none }

enum LoanPurposeType { agriculture, commerce, services, transportation, construction, family, other }


class Customer {
  // Section 1: Borrower Information
  // Section 1: Borrower Information
  final IdCardType? idCardType;
  final String? idCardPhotoPath;
  final String? fullNameKhmer;
  final String? fullNameLatin;
  final DateTime? dateOfBirth;
  final String? idNumber;
  final String? portfolioOfficerName;

  // Section 2: Loan/Pawn Details
  final double? requestedAmount;
  final List<LoanPurposeType>? loanPurposes;
  final String? purposeDetails;
  final product_type.ProductType? productType;
  final String? desiredLoanTerm;
  final DateTime? requestedDisbursementDate;

  // Section 3: Borrower Photos
  final String? borrowerNidPhotoPath;
  final String? borrowerHomeOrLandPhotoPath;
  final String? borrowerBusinessPhotoPath;

  // Section 4: Guarantor Photos
  final String? guarantorName;
  final String? guarantorPhone;
  final String? guarantorNidPhotoPath;
  final String? guarantorHomeOrLandPhotoPath;
  final String? guarantorBusinessPhotoPath;
  final String? profilePhotoPath;

  final String id;
  final String name;
  final String phone;
  final String nid;
  final String? profileImage;
  final double loanAmount;
  final DateTime loanStartDate;
  final DateTime? loanEndDate;
  final LoanStatus loanStatus;
  final double? interestRate;
  final String? loanPurpose;

  Customer({
    // Borrower Info
    this.idCardType,
    this.idCardPhotoPath,
    this.fullNameKhmer,
    this.fullNameLatin,
    this.dateOfBirth,
    this.idNumber,
    this.portfolioOfficerName,

    // Loan Details
    this.requestedAmount,
    this.loanPurposes,
    this.purposeDetails,
    this.productType,
    this.desiredLoanTerm,
    this.requestedDisbursementDate,

    // Borrower Photos
    this.borrowerNidPhotoPath,
    this.borrowerHomeOrLandPhotoPath,
    this.borrowerBusinessPhotoPath,

    // Guarantor Photos
    this.guarantorName,
    this.guarantorPhone,
    this.guarantorNidPhotoPath,
    this.guarantorHomeOrLandPhotoPath,
    this.guarantorBusinessPhotoPath,
    this.profilePhotoPath,

    // Original fields
    required this.id,
    required this.name, // Keep for now to avoid breaking UI, will replace with fullNameLatin
    required this.phone,
    required this.nid,
    this.profileImage,
    required this.loanAmount,
    required this.loanStartDate,
    this.loanEndDate,
    required this.loanStatus,
    this.interestRate,
    this.loanPurpose,
  });

  String get formattedStartDate {
    return '${_getMonthName(loanStartDate.month)} ${loanStartDate.day}, ${loanStartDate.year}';
  }

  // JSON serialization methods for local storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'nid': nid,
      'profileImage': profileImage,
      'loanAmount': loanAmount,
      'loanStartDate': loanStartDate.toIso8601String(),
      'loanEndDate': loanEndDate?.toIso8601String(),
      'loanStatus': loanStatus.toString(),
      'interestRate': interestRate,
      'loanPurpose': loanPurpose,

      // Extended fields
      'idCardType': idCardType?.toString(),
      'idCardPhotoPath': idCardPhotoPath,
      'fullNameKhmer': fullNameKhmer,
      'fullNameLatin': fullNameLatin,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'idNumber': idNumber,
      'portfolioOfficerName': portfolioOfficerName,
      'requestedAmount': requestedAmount,
      'loanPurposes': loanPurposes?.map((e) => e.toString()).toList(),
      'purposeDetails': purposeDetails,
      'productType': productType?.toString(),
      'desiredLoanTerm': desiredLoanTerm,
      'requestedDisbursementDate': requestedDisbursementDate?.toIso8601String(),
      'borrowerNidPhotoPath': borrowerNidPhotoPath,
      'borrowerHomeOrLandPhotoPath': borrowerHomeOrLandPhotoPath,
      'borrowerBusinessPhotoPath': borrowerBusinessPhotoPath,
      'guarantorName': guarantorName,
      'guarantorPhone': guarantorPhone,
      'guarantorNidPhotoPath': guarantorNidPhotoPath,
      'guarantorHomeOrLandPhotoPath': guarantorHomeOrLandPhotoPath,
      'guarantorBusinessPhotoPath': guarantorBusinessPhotoPath,
      'profilePhotoPath': profilePhotoPath,
    };
  }

  factory Customer.fromJson(Map<String, dynamic> json) {
    return Customer(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      nid: json['nid'] ?? '',
      profileImage: json['profileImage'],
      loanAmount: (json['loanAmount'] ?? 0).toDouble(),
      loanStartDate:
          DateTime.tryParse(json['loanStartDate'] ?? '') ?? DateTime.now(),
      loanEndDate:
          json['loanEndDate'] != null
              ? DateTime.tryParse(json['loanEndDate'])
              : null,
      loanStatus: _parseLoanStatus(json['loanStatus']),
      interestRate: json['interestRate']?.toDouble(),
      loanPurpose: json['loanPurpose'],

      // Extended fields
      idCardType: _parseIdCardType(json['idCardType']),
      idCardPhotoPath: json['idCardPhotoPath'],
      fullNameKhmer: json['fullNameKhmer'],
      fullNameLatin: json['fullNameLatin'],
      dateOfBirth:
          json['dateOfBirth'] != null
              ? DateTime.tryParse(json['dateOfBirth'])
              : null,
      idNumber: json['idNumber'],
      portfolioOfficerName: json['portfolioOfficerName'],
      requestedAmount: json['requestedAmount']?.toDouble(),
      loanPurposes:
          json['loanPurposes'] != null
              ? (json['loanPurposes'] as List)
                  .map((e) => _parseLoanPurposeType(e.toString()))
                  .toList()
              : null,
      purposeDetails: json['purposeDetails'],
      productType: _parseProductType(json['productType']),
      desiredLoanTerm: json['desiredLoanTerm'],
      requestedDisbursementDate:
          json['requestedDisbursementDate'] != null
              ? DateTime.tryParse(json['requestedDisbursementDate'])
              : null,
      borrowerNidPhotoPath: json['borrowerNidPhotoPath'],
      borrowerHomeOrLandPhotoPath: json['borrowerHomeOrLandPhotoPath'],
      borrowerBusinessPhotoPath: json['borrowerBusinessPhotoPath'],
      guarantorName: json['guarantorName'],
      guarantorPhone: json['guarantorPhone'],
      guarantorNidPhotoPath: json['guarantorNidPhotoPath'],
      guarantorHomeOrLandPhotoPath: json['guarantorHomeOrLandPhotoPath'],
      guarantorBusinessPhotoPath: json['guarantorBusinessPhotoPath'],
      profilePhotoPath: json['profilePhotoPath'],
    );
  }

  // Use fullNameLatin if available, otherwise fallback to the original name field.
  String get displayName => fullNameLatin ?? name;

  Color get statusColor {
    switch (loanStatus) {
      case LoanStatus.draft:
        return Colors.grey;
      case LoanStatus.pending:
        return Colors.orange;
      case LoanStatus.approved:
        return Colors.blue;
      case LoanStatus.disbursed:
        return Colors.green;
      case LoanStatus.completed:
        return Colors.purple;
      case LoanStatus.rejected:
        return Colors.red;
    }
  }

  String _getMonthName(int month) {
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return monthNames[month - 1];
  }

  IconData get statusIcon {
    switch (loanStatus) {
      case LoanStatus.draft:
        return Icons.edit;
      case LoanStatus.pending:
        return Icons.pending_actions;
      case LoanStatus.approved:
        return Icons.verified;
      case LoanStatus.disbursed:
        return Icons.monetization_on;
      case LoanStatus.completed:
        return Icons.check_circle;
      case LoanStatus.rejected:
        return Icons.cancel;
    }
  }

  // Static parsing methods for JSON deserialization
  static LoanStatus _parseLoanStatus(String? status) {
    if (status == null) return LoanStatus.pending;
    return LoanStatus.values.firstWhere(
      (e) => e.toString() == status,
      orElse: () => LoanStatus.pending,
    );
  }

  static IdCardType? _parseIdCardType(String? type) {
    if (type == null) return null;
    return IdCardType.values.firstWhere(
      (e) => e.toString() == type,
      orElse: () => IdCardType.cambodianIdentity,
    );
  }

  static LoanPurposeType _parseLoanPurposeType(String type) {
    return LoanPurposeType.values.firstWhere(
      (e) => e.toString() == type,
      orElse: () => LoanPurposeType.agriculture,
    );
  }

  static product_type.ProductType? _parseProductType(String? type) {
    if (type == null) return null;
    return product_type.ProductType.values.firstWhere(
      (e) => e.toString() == type,
      orElse: () => product_type.ProductType.daily,
    );
  }
}
